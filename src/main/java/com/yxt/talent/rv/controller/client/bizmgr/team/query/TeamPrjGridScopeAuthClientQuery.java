package com.yxt.talent.rv.controller.client.bizmgr.team.query;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.talent.rv.controller.manage.prj.user.query.PrjGridScopeAuthQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nonnull;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = true)
@Schema(name = "学员端-我的团队-人才盘点-项目维度-盘点结果列表-详情-盘点九宫格")
public class TeamPrjGridScopeAuthClientQuery extends TeamScopeAuthClientQuery {
    @Schema(description = "盘点项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String projectId;

    @Schema(description = "x轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisX;

    @Schema(description = "y轴维度id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String axisY;

    @Schema(description = "部门ids")
    private List<String> deptIds;

    @Schema(description = "岗位ids")
    private List<String> positionIds;

    @Schema(description = "职级ids")
    private List<String> gradeIds;

    @Schema(description = "员工状态 0：禁用 1：启用，2:删除，3:排除删除状态, -1：查询所有（默认）")
    private int status = -1;

    @Schema(description = "x轴维度值（1-3对应低至高）")
    private Integer valueX;

    @Schema(description = "y轴维度值（1-3对应低至高）")
    private Integer valueY;

    @Schema(description = "查询参数")
    private String searchKey;

    @Schema(description = "维度组合ID since 5.8")
    private String dimCombId;

    @Schema(description = "排序维度id")
    private String sortDimId;

    @Nonnull
    @JsonIgnore
    @JSONField(serialize = false)
    public PrjGridScopeAuthQuery toPrjGridScopeAuthQuery() {
        return Assembler.INSTANCE.convert(this);
    }

    @Mapper
    public interface Assembler {
        Assembler INSTANCE = Mappers.getMapper(Assembler.class);

        @Mapping(target = "userIds", ignore = true)
        PrjGridScopeAuthQuery convert(
                TeamPrjGridScopeAuthClientQuery teamPrjGridScopeAuthClientQuery);
    }

}
