package com.yxt.talent.rv.controller.manage.xpd.rule.enums;

import lombok.Getter;

/**
 * 盘点项目的分层方式
 *
 * <AUTHOR>
 */
@Getter
public enum XpdLevelTypeEnum {

    /**
     * 人员分层方式
     */
    RATIO_VALUE(0, "按比例"),
    FIXED_VALUE(1, "按固定值"),
    JUDGE_RULE(2, "综合判断"),
    ;

    private final Integer code;
    private final String name;

    XpdLevelTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static XpdLevelTypeEnum getDefault() {
        return RATIO_VALUE;
    }

    public static boolean byRatio(Integer code) {
        return RATIO_VALUE.getCode().equals(code);
    }

    public static boolean byFixedValue(Integer code) {
        return FIXED_VALUE.getCode().equals(code);
    }
}
