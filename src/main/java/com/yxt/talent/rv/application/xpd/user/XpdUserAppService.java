package com.yxt.talent.rv.application.xpd.user;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.aom.base.entity.part.ActivityParticipationMember;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.Paging;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.enums.DeleteEnum;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.talent.rv.application.activity.dto.ActMemberUser;
import com.yxt.talent.rv.application.activity.dto.ActMemberUserCriteria;
import com.yxt.talent.rv.application.prj.dim.dto.PrjDimScoreDTO;
import com.yxt.talent.rv.application.prj.user.dto.PrjUserDimDTO;
import com.yxt.talent.rv.application.user.dto.ScopeAuthDTO;
import com.yxt.talent.rv.application.xpd.common.dto.XpdResultUserLevelDTO;
import com.yxt.talent.rv.application.xpd.common.dto.XpdUserCountDto;
import com.yxt.talent.rv.application.xpd.result.XpdResultAppService;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjResultScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamPrjUserResultScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.team.viewobj.TeamPrjUserResultClientVO;
import com.yxt.talent.rv.controller.client.general.prj.viewobj.PrjClientVO;
import com.yxt.talent.rv.controller.client.general.prj.viewobj.PrjSubUserClientVO;
import com.yxt.talent.rv.controller.manage.prj.user.command.PrjUserResultSuggestionAddCmd;
import com.yxt.talent.rv.controller.manage.prj.user.query.PrjGridScopeAuthQuery;
import com.yxt.talent.rv.controller.manage.prj.user.query.PrjUserQuery;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjResultSubUserVO;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserVO;
import com.yxt.talent.rv.controller.manage.xpd.result.query.XpdResultQuery;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombGridResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimDetailVO;
import com.yxt.talent.rv.controller.manage.xpd.user.command.XpdUserLatestResultVO;
import com.yxt.talent.rv.controller.manage.xpd.user.command.XpdUserResultSubVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.auth.AuthorizationService;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.OrginitAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.SpsdAclServiceImpl;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 盘点人员管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XpdUserAppService {
    private static final String NAV_CODE = "sp_gwnl_talentrv_calibration";

    private final L10nAclService l10nAclService;
    private final OrginitAclService orginitAclService;
    private final UdpAclService udpAclService;
    private final I18nTranslator i18nTranslator;
    private final AuthorizationService authorizationService;
    private final XpdResultAppService xpdResultAppService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdLevelMapper xpdLevelMapper;
    private final XpdMapper xpdMapper;
    private final XpdActivityMemberStatisticsMapper xpdActivityMemberStatisticsMapper;
    private final XpdActivityParticipationMemberMapper xpdActivityParticipationMemberMapper;
    private final XpdUserExtMapper xpdUserExtMapper;
    private final RvActivityParticipationMemberMapper rvActivityParticipationMemberMapper;
    private final ActivityParticipationService activityParticipationService;
    private final XpdResultUserDimMapper resultUserDimMapper;
    private final XpdDimMapper xpdDimMapper;
    private final SpsdAclServiceImpl spsdAclService;
    private final XpdGridLevelMapper gridLevelMapper;

    /**
     * [新盘点]我的团队-人才盘点-个人维度
     */
    public PagingList<TeamPrjUserResultClientVO> searchProjectResultUserList(PageRequest pageRequest, UserCacheDetail userCache, TeamPrjUserResultScopeAuthClientQuery search) {

        String orgId = userCache.getOrgId();
        String curUserId = userCache.getUserId();
        String keyword = search.getKeyword();
        String sourceCode = userCache.getSourceCode();

        fillScopeAuth(search, orgId, curUserId);
        log.debug("fillScopeAuth search:{}", JSON.toJSONString(search));

        Page<TeamPrjUserResultClientVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        List<String> qwUserIds = orginitAclService.searchContactList(orgId, keyword, 1, sourceCode);

        boolean enableLocalization = i18nTranslator.isEnableLocalization(orgId);
        Set<String> l10nUserIds = l10nAclService.searchContentByKey(enableLocalization, List.of(orgId), ResourceTypeEnum.USER, keyword);
        if (CollectionUtils.isNotEmpty(l10nUserIds)) {
            qwUserIds.addAll(l10nUserIds);
        }

        IPage<TeamPrjUserResultClientVO> resultList = udpLiteUserMapper.selectTeamXpdUserResults(page, orgId, curUserId, search, qwUserIds);
        i18nTranslator.translate(orgId, userCache.getLocale(), resultList.getRecords());

        PagingList<TeamPrjUserResultClientVO> result = BeanCopierUtil.toPagingList(resultList);

        if (CollectionUtils.isNotEmpty(resultList.getRecords())) {
            // 设置最新盘点结果
            fillLatestResult(orgId, result.getDatas());
            // 设置盘点次数 仅统计发布中、已结束项目
            fillXpdCount(orgId, result.getDatas());
            //国际化
            if (enableLocalization) {
                Map<String, IdName> idNameMap = new HashMap<>(8);
                Set<String> deptIds = result.getDatas().stream().map(TeamPrjUserResultClientVO::getDeptId).collect(Collectors.toSet());
                List<IdName> idNames = udpAclService.getDeptInfoByIds(orgId, new ArrayList<>(deptIds));
                if (CollectionUtils.isNotEmpty(idNames)) {
                    idNameMap = StreamUtil.list2map(idNames, IdName::getId);
                }
                Map<String, IdName> finalIdNameMap = idNameMap;
                result.getDatas().forEach(e -> {
                    if (MapUtils.isNotEmpty(finalIdNameMap) && null != finalIdNameMap.get(e.getDeptId())) {
                        e.setDeptName(finalIdNameMap.get(e.getDeptId()).getName());
                    }
                });
            }
        }
        return result;
    }

    public void fillScopeAuth(TeamScopeAuthClientQuery search, String orgId, String currUserId) {
        String teamId = search.getTeamId();
        List<String> scopeDeptIds = search.getScopeDeptIds();
        List<String> scopeUserIds = search.getScopeUserIds();
        Validate.isTrue(
                !(CollectionUtils.isEmpty(scopeUserIds) && CollectionUtils.isEmpty(scopeDeptIds) &&
                        StringUtils.isBlank(teamId)), ExceptionKeys.AUTH_PARAM_ERROR);

        if (CollectionUtils.isNotEmpty(scopeDeptIds)) {
            search.setScopeDeptIds(udpAclService.exchangeSubDeptIds(orgId, scopeDeptIds));
        }
        if (CollectionUtils.isNotEmpty(scopeUserIds)) {
            search.setScopeUserIds(scopeUserIds);
        }
        if (StringUtils.isNotBlank(teamId)) {
            ScopeAuthDTO scopeAuthDTO = authorizationService.getTeamMgrScopeAuth(orgId, currUserId, teamId);
            search.setScopeDeptIds(getIntersection(search.getScopeDeptIds(), scopeAuthDTO.getScopeDeptIds()));
            search.setScopeUserIds(getIntersection(search.getScopeUserIds(), scopeAuthDTO.getScopeUserIds()));
        }
    }

    private List<String> getIntersection(List<String> list1, List<String> list2) {
        if (CollectionUtils.isEmpty(list1)) {
            return list2;
        }
        list1.retainAll(list2);
        return list1;
    }

    /**
     * 设置最新盘点结果
     */
    private void fillLatestResult(String orgId, List<TeamPrjUserResultClientVO> datas) {
        List<String> userIds = datas.stream().map(TeamPrjUserResultClientVO::getUserId).collect(Collectors.toList());

        List<XpdResultUserPO> userResults = xpdResultUserMapper.findResultsByUserIds(orgId, new HashSet<>(userIds));
        Map<String, List<XpdResultUserPO>> userResultsMap = userResults.stream().collect(Collectors.groupingBy(XpdResultUserPO::getUserId));

        Set<String> levelIds = StreamUtil.map2set(userResults, XpdResultUserPO::getXpdLevelId);
        Map<String, XpdLevelPO> levelMap = StreamUtil.list2map(xpdLevelMapper.listByIds(levelIds), XpdLevelPO::getId);

        datas.forEach(data -> {
            String userId = data.getUserId();
            if (userResultsMap.containsKey(userId)) {
                List<XpdResultUserPO> results =
                        userResultsMap.get(userId).stream().sorted(Comparator.comparing(XpdResultUserPO::getUpdateTime).reversed()).collect(Collectors.toList());
                XpdResultUserPO lastestResult = results.get(0);
                if (levelMap.containsKey(lastestResult.getXpdLevelId())) {
                    data.setLevelNameI18n(levelMap.get(lastestResult.getXpdLevelId()).getLevelNameI18n());
                    data.setLevelName(levelMap.get(lastestResult.getXpdLevelId()).getLevelName());
                }
            }
        });
    }

    /**
     * 设置盘点次数
     */
    private void fillXpdCount(String orgId, List<TeamPrjUserResultClientVO> datas) {
        List<String> userIds = datas.stream().map(TeamPrjUserResultClientVO::getUserId).collect(Collectors.toList());

        List<XpdUserCountDto> countList = xpdMapper.countByUserIds(orgId, userIds);
        if (!countList.isEmpty()) {
            Map<String, Integer> countMap = countList.stream().collect(Collectors.toMap(XpdUserCountDto::getUserId, XpdUserCountDto::getCountVal, (v1, v2) -> v2));
            datas.forEach(pr -> {
                if (countMap.containsKey(pr.getUserId())) {
                    pr.setRvCount(countMap.get(pr.getUserId()));
                }
            });
        }
    }

    /**
     * [新盘点]我的团队-人才盘点-项目维度
     */
    public PagingList<PrjClientVO> searchProjectClientList(PageRequest pageRequest, UserCacheDetail userCache, TeamPrjResultScopeAuthClientQuery search) {

        List<Integer> prjStatusList = new ArrayList<>();
        // 只查进行中、已结束的项目
        if (search.getPrjStatus() == null || search.getPrjStatus() == -1) {
            prjStatusList.add(2);
            prjStatusList.add(3);
        } else {
            prjStatusList.add(search.getPrjStatus());
        }

        String orgId = userCache.getOrgId();
        String currUserId = userCache.getUserId();

        fillScopeAuth(search, orgId, currUserId);

        Page<PrjClientVO> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<PrjClientVO> resultList = udpLiteUserMapper.selectPrjClientPage(pageable, orgId, prjStatusList, search);

        // 设置 完成率
        List<String> actvIds = resultList.getRecords().stream().map(PrjClientVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(actvIds)) {
            Map<String, PrjClientVO> prjClientVOs = StreamUtil.list2map(xpdActivityMemberStatisticsMapper.avgProgressByActvIds(orgId, actvIds), PrjClientVO::getId);
            for (PrjClientVO a : resultList.getRecords()) {
                a.setProgress(prjClientVOs.containsKey(a.getId()) ? prjClientVOs.get(a.getId()).getProgress() : BigDecimal.ZERO);
            }
        }

        return BeanCopierUtil.toPagingList(resultList);
    }

    /**
     * 盘点结果【项目维度】- 项目查看界面用户列表
     */
    public PagingList<PrjSubUserClientVO> searchProjectClientUserList(PageRequest pageRequest, UserCacheDetail operator, String projectId, String keyword,
                                                                      TeamScopeAuthClientQuery search) {

        // 根据我的团队id拿到此人有权限查看的人员id和部门id
        String orgId = operator.getOrgId();
        String currUserId = operator.getUserId();

        fillScopeAuth(search, orgId, currUserId);

        List<String> qwUserIds = orginitAclService.searchContactList(operator.getOrgId(), keyword, 1, operator.getSourceCode());
        Page<PrjSubUserClientVO> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());

        boolean enableLocalization = i18nTranslator.isEnableLocalization(orgId);
        Set<String> l10nUserIds = l10nAclService.searchContentByKey(enableLocalization, List.of(orgId), ResourceTypeEnum.USER, keyword);
        if (CollectionUtils.isNotEmpty(l10nUserIds)) {
            qwUserIds.addAll(new ArrayList<>(l10nUserIds));
        }

        IPage<PrjSubUserClientVO> resultList = xpdActivityParticipationMemberMapper.selectMyScopeUserInPrj(pageable, operator.getOrgId(), projectId, SqlUtil.escapeSql(keyword),
                search, qwUserIds);

        if (enableLocalization) {
            i18nTranslator.translate(orgId, operator.getLocale(), resultList.getRecords());
        }
        if (CollectionUtils.isNotEmpty(resultList.getRecords()) && enableLocalization) {
            Map<String, IdName> idNameMap = new HashMap<>(8);
            Set<String> deptIds = resultList.getRecords().stream().map(PrjSubUserClientVO::getDeptId).collect(Collectors.toSet());
            List<IdName> idNames = udpAclService.getDeptInfoByIds(orgId, new ArrayList<>(deptIds));
            if (CollectionUtils.isNotEmpty(idNames)) {
                idNameMap = StreamUtil.list2map(idNames, IdName::getId);
            }
            Map<String, IdName> finalIdNameMap = idNameMap;
            resultList.getRecords().forEach(e -> {
                if (MapUtils.isNotEmpty(finalIdNameMap) && null != finalIdNameMap.get(e.getDeptId())) {
                    e.setDeptName(finalIdNameMap.get(e.getDeptId()).getName());
                }
            });
        }
        PagingList<PrjSubUserClientVO> result = BeanCopierUtil.toPagingList(resultList);

        // 设置直属or管辖的标识
        List<String> managerUserIds = authorizationService.getClientAuthUserIds(operator, true);
        result.getDatas().stream().filter(item -> managerUserIds.contains(item.getUserId())).forEach(item -> item.setDirectly(1));

        return result;
    }

    /**
     * 盘点结果九宫格查询
     *
     * @param userCache 机构id
     */
    @SuppressWarnings({"OverlyLongMethod"})
    public List<XpdDimCombGridResultVO> gridview(UserCacheDetail userCache, PrjGridScopeAuthQuery search) {

        if (StringUtils.isEmpty(search.getDimCombId())) {
            return new ArrayList<>();
        }

        String orgId = userCache.getOrgId();
        XpdResultQuery query = new XpdResultQuery();
        query.setQueryType(2);
        query.setTargetId(search.getDimCombId());
        query.setScopeUserIds(search.getScopeUserIds());
        query.setScopeDeptIds(search.getScopeDeptIds());
        query.setSearchKey(search.getSearchKey());
        query.setSortDimId(search.getSortDimId());

        if (StringUtils.isNotEmpty(search.getSearchKey())) {
            List<String> qwUserIds = orginitAclService.searchContactList(userCache.getOrgId(), search.getSearchKey(), 1, userCache.getSourceCode());

            boolean enableLocalization = i18nTranslator.isEnableLocalization(orgId);
            Set<String> l10nUserIds = l10nAclService.searchContentByKey(enableLocalization, List.of(orgId), ResourceTypeEnum.USER, search.getSearchKey());
            if (CollectionUtils.isNotEmpty(l10nUserIds)) {
                qwUserIds.addAll(new ArrayList<>(l10nUserIds));
            }
            query.setScopeDeptIds(null);
            query.setScopeUserIds(new ArrayList<>(l10nUserIds));
        }

        return xpdResultAppService.getDimCombGridResult(userCache, search.getProjectId(), search.getDimCombId(), query);
    }

    /**
     * 人员查看界面列表【用户维度】
     *
     * @param pageRequest
     * @param userId      需要查看的人员ID
     */
    public PagingList<PrjResultSubUserVO> searchProjectResultSubUserList(PageRequest pageRequest, UserCacheDetail userCache, String userId) {
        String orgId = userCache.getOrgId();
        String lang = userCache.getLocale();

        Page<PrjResultSubUserVO> pageable = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<PrjResultSubUserVO> resultList = xpdActivityParticipationMemberMapper.listPrjByUserId(pageable, orgId, userId);
        PagingList<PrjResultSubUserVO> result = BeanCopierUtil.toPagingList(resultList);

        List<String> actvIds = StreamUtil.mapList(result.getDatas(), PrjResultSubUserVO::getActvId);
        List<String> xpdIds = StreamUtil.mapList(result.getDatas(), PrjResultSubUserVO::getProjectId);
        // 盘点进度
        List<ActivityMemberStatistics> stats = xpdActivityMemberStatisticsMapper.listCompRate(orgId, actvIds, userId);
        // 盘点结果
        List<PrjResultSubUserVO> prjResults = xpdResultUserMapper.findResultsByActvIdsAndUserId(orgId, actvIds, userId);
        i18nTranslator.translate(orgId, lang, prjResults);
        // 发展建议
        List<XpdUserExtPO> userExts = xpdUserExtMapper.selectByXpdIdsAndUserId(orgId, xpdIds, userId);
        for (PrjResultSubUserVO data : result.getDatas()) {
            // 填充盘点进度
            for (ActivityMemberStatistics stat : stats) {
                if (data.getActvId().equals(stat.getActvId()) && stat.getActvCompletedRate() != null) {
                    BigDecimal actvCompletedRate = stat.getActvCompletedRate();
                    actvCompletedRate = actvCompletedRate.multiply(new BigDecimal(100).setScale(0, RoundingMode.HALF_UP));
                    data.setProgress(actvCompletedRate);
                    break;
                }
            }
            // 填充盘点结果
            for (PrjResultSubUserVO resultUser : prjResults) {
                if (data.getProjectId().equals(resultUser.getProjectId())) {
                    data.setLevelName(resultUser.getLevelName());
                    data.setLevelNameI18n(resultUser.getLevelNameI18n());
                    break;
                }
            }
            // 填充发展建议
            for (XpdUserExtPO userExt : userExts) {
                if (data.getProjectId().equals(userExt.getXpdId())) {
                    data.setSuggestion(userExt.getSuggestion());
                    break;
                }
            }
        }

        // 填充最近盘点维度结果
        if (CollectionUtils.isNotEmpty(result.getDatas())) {
            for (PrjResultSubUserVO data : result.getDatas()) {
                String xpdId = data.getProjectId();
                XpdResultUserDimDetailVO userDimDetail = xpdResultAppService.getUserDimDetail(userCache, xpdId, userId);
                data.setUserDimDetail(userDimDetail);
            }
        }
        return result;
    }

    public XpdUserLatestResultVO getUserLatestXpdResult(UserCacheDetail userCache, String userId) {
        // 用户基本信息
        XpdUserLatestResultVO cmd = new XpdUserLatestResultVO();
        String orgId = userCache.getOrgId();
        String locale = userCache.getLocale();
        boolean enableLocalization = i18nTranslator.isEnableLocalization(orgId);
        UdpLiteUserPO user = udpLiteUserMapper.selectByUserId(orgId, userId);
        if (user != null) {
            if (enableLocalization) {
                i18nTranslator.translate(orgId, locale, user);
            }
            BeanCopierUtil.copy(user, cmd);
            cmd.setUserId(user.getId());
        }

        // 设置盘点结果
        // 最新一次盘点
        List<PrjResultSubUserVO> resultList = xpdActivityParticipationMemberMapper.listPrjByUserIdNoPage(orgId, userId);
        PrjResultSubUserVO latestXpd = resultList.stream().filter(r -> r.getProjectStatus() == 3).findFirst().orElse(null);
        if (latestXpd != null) {
            String xpdId = latestXpd.getProjectId();
            XpdResultUserLevelDTO userResult = xpdResultUserMapper.findResultByXpdIdAndUserId(orgId, xpdId, userId);
            if (userResult != null) {
                if (enableLocalization) {
                    i18nTranslator.translate(orgId, locale, userResult);
                }
                cmd.setLevelName(userResult.getLevelName());
            }
            // 盘点宫格
            XpdResultUserDimDetailVO userDimDetail = xpdResultAppService.getUserDimDetail(userCache, xpdId, userId);
            cmd.setUserDimDetail(userDimDetail);
        }
        return cmd;
    }

    /**
     * 保存发展建议
     *
     * @param userCache
     * @param bean      入参
     */
    public void saveSuggestion(UserCacheBasic userCache, PrjUserResultSuggestionAddCmd bean) {
        String orgId = userCache.getOrgId();
        XpdUserExtPO xpdUserExt = xpdUserExtMapper.selectByXpdIdAndUserId(orgId, bean.getUserId(), bean.getProjectId());
        Long memberId = findActvMemberId(orgId, bean);
        Validate.isNotNull(memberId, ExceptionKeys.XPD_USER_MEMBERID_NULL);
        if (xpdUserExt == null) {
            xpdUserExt = new XpdUserExtPO();
            xpdUserExt.setId(ApiUtil.getUuid());
            xpdUserExt.setOrgId(orgId);
            xpdUserExt.setXpdId(bean.getProjectId());
            xpdUserExt.setUserId(bean.getUserId());
            xpdUserExt.setActvMemberId(memberId);
            xpdUserExt.setSuggestion(bean.getSuggestion());
            xpdUserExt.setDeleted(DeleteEnum.NOT_DELETED.getCode());
            xpdUserExt.setCreateUserId(userCache.getUserId());
            xpdUserExt.setCreateTime(DateUtil.currentTime());
            xpdUserExt.setUpdateUserId(userCache.getUserId());
            xpdUserExt.setUpdateTime(DateUtil.currentTime());
        } else {
            xpdUserExt.setActvMemberId(memberId);
            xpdUserExt.setSuggestion(bean.getSuggestion());
            xpdUserExt.setUpdateUserId(userCache.getUserId());
            xpdUserExt.setUpdateTime(DateUtil.currentTime());
        }
        xpdUserExtMapper.insertOrUpdate(xpdUserExt);
    }

    @Nullable
    private Long findActvMemberId(String orgId, PrjUserResultSuggestionAddCmd bean) {
        if (bean.getMemberId() != null) {
            return bean.getMemberId();
        }
        ActivityParticipationMember activityParticipationMember =
                xpdActivityParticipationMemberMapper.selectByXpdIdAndUserId(orgId, bean.getProjectId(), bean.getUserId());
        if (activityParticipationMember != null) {
            return activityParticipationMember.getId();
        }
        return null;
    }

    public XpdUserResultSubVO getXpdUserResult(UserCacheDetail userCache, String actvId, String userId) {

        String orgId = userCache.getOrgId();

        XpdUserResultSubVO ret = new XpdUserResultSubVO();

        XpdPO xpd = xpdMapper.selectByAomPrjId(orgId, actvId);
        if (xpd == null) {
            return ret;
        }

        // 查询人员的信息
        UdpLiteUserPO user = udpLiteUserMapper.selectByUserId(orgId, userId);
        if (user == null) {
            throw new ApiException(ExceptionKeys.USER_NOT_EXISTED);
        }
        boolean enableLocalization = i18nTranslator.isEnableLocalization(orgId);
        if (enableLocalization) {
            i18nTranslator.translate(orgId, userCache.getLocale(), user);
        }

        ret.setUserId(userId);
        ret.setFullName(user.getFullname());
        ret.setUserName(user.getUsername());
        ret.setDeptName(user.getDeptName());
        ret.setPosition(user.getPositionName());
        ret.setImgUrl(user.getImgUrl());

        XpdResultUserDimDetailVO userDimDetail = xpdResultAppService.getUserDimDetail(userCache, xpd.getId(), userId);
        ret.setUserDimDetail(userDimDetail);
        return ret;
    }

    /**
     * 查询盘点人员分页列表
     *
     * @param userCache
     * @param pageRequest 分页数据
     * @param projectId   项目id
     * @param search      查询入参
     */
    public PagingList<PrjUserVO> findXpdPrjUserPage(UserCacheDetail userCache, PageRequest pageRequest, String projectId,
        PrjUserQuery search) {

        String xpdId = projectId;
        // 查询维度
        String orgId = userCache.getOrgId();

        XpdPO xpdPO = xpdMapper.selectById(projectId);

        // 只能查管辖范围内的人
        // 获取所有人
        List<String> allPrjUserIds =
            rvActivityParticipationMemberMapper.findAllUserIdByActId(orgId, xpdPO.getAomPrjId());
        List<String> authUserIds = authorizationService.getAuthUserIdsRange(userCache, NAV_CODE,
            "sp_talentrv_calibrationmemberlist_dep_extent", allPrjUserIds);
        if (!"1".equals(userCache.getAdmin()) && CollectionUtils.isEmpty(authUserIds)) {
            int offset = (int) ((pageRequest.getCurrent() - 1) * pageRequest.getSize());
            return new PagingList<>(new ArrayList<>(), new Paging(pageRequest.getSize(), offset, 0, 0));
        }

        // 企业加密
        List<String> qwUserIds = orginitAclService.searchContactList(userCache.getOrgId(), search.getKeyword(), 1,
            userCache.getSourceCode());
        //国际化人名模糊搜索
        boolean enableLocalization = l10nAclService.isEnableLocalization(userCache.getOrgId());
        Set<String> l10nUserIds = l10nAclService.searchContentByKey(enableLocalization, List.of(userCache.getOrgId()),
            ResourceTypeEnum.USER, search.getKeyword());
        if (CollectionUtils.isNotEmpty(l10nUserIds)) {
            qwUserIds.addAll(l10nUserIds);
        }

        Page<ActMemberUser> pageParam = ApiUtil.toPage(pageRequest);
        ActMemberUserCriteria searchParam = new ActMemberUserCriteria();

        searchParam.setDeptIds(search.getDeptIds());
        searchParam.setQwUserIds(qwUserIds);
        if (CollectionUtils.isEmpty(qwUserIds)) {
            searchParam.setKeyword(ApiUtil.getFiltedLikeString(search.getKeyword()));
        }
        Long partId = activityParticipationService.getParticipationId(orgId, xpdPO.getAomPrjId());
        searchParam.setParticipationId(partId);
        searchParam.setActvId(xpdPO.getAomPrjId());
        searchParam.setPrjId(xpdPO.getAomPrjId());
        searchParam.setAllUserIds(authUserIds);

        IPage<ActMemberUser> pageData = rvActivityParticipationMemberMapper.listPrjUserPage(pageParam, orgId, searchParam);

        // 查询盘点人员数据
        Page<PrjUserDimDTO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        //IPage<PrjUserDimDTO> ipage = prjUserMapper.searchPage(page, userCache.getOrgId(), projectId, search, qwUserIds);
        //国际化翻译
        l10nAclService.translateList(enableLocalization, List.of(userCache.getOrgId()), userCache.getLocale(),
            PrjUserDimDTO.class, page.getRecords());

        // 查询人员盘点结果
        Map<String, IdName> idNameMap = new HashMap<>(8);

        Map<String, String> gridLevelMap;
        Map<String, List<XpdResultUserDimPO>> dimUserMap = null;
        PagingList<PrjUserVO> data = BeanCopierUtil.toPagingList(pageData, ActMemberUser.class, PrjUserVO.class);
        List<PrjUserVO> resList = new ArrayList<>();
        for (ActMemberUser record : pageData.getRecords()) {
            PrjUserVO res = new PrjUserVO();
            res.setUserId(record.getUserId());
            res.setUserName(record.getUsername());
            res.setFullName(record.getFullname());
            res.setDeptName(record.getDeptName());
            res.setStatus(record.getStatus());
            res.setPositionName(record.getPositionName());
            resList.add(res);
        }
        data.setDatas(resList);


        if (CollectionUtils.isEmpty(pageData.getRecords())) {
            PagingList<PrjUserVO> pagingList = new PagingList<>();
            pagingList.setPaging(data.getPaging());
            pagingList.setDatas(new ArrayList<>());
            return pagingList;
        }

        List<ActMemberUser> records = pageData.getRecords();

        List<String> userIds = records.stream().map(ActMemberUser::getUserId).toList();
        List<String> deptIds = records.stream().map(ActMemberUser::getDeptId).toList();
        // 查询个人维度
        List<XpdResultUserDimPO> resultUserDimList =
            resultUserDimMapper.findByXpdIdAndUserIds(orgId, xpdPO.getId(), userIds);
        dimUserMap = resultUserDimList.stream().collect(Collectors.groupingBy(XpdResultUserDimPO::getUserId));


        // 项目维度数据
        List<XpdDimPO> xpdDimList = xpdDimMapper.selectByXpdId(orgId, projectId, null);
        List<String> dimIdList = xpdDimList.stream().map(XpdDimPO::getSdDimId).toList();
        List<DimensionList4Get> baseDimList = spsdAclService.getBaseDimDetail(orgId, dimIdList);

        Map<String, String> dimNameMap =
            StreamUtil.list2map(baseDimList, DimensionList4Get::getId, DimensionList4Get::getDmName);
        // 项目维度等级
        List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByXpdId(orgId, xpdId);
        gridLevelMap = StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getId, XpdGridLevelPO::getLevelName);

        if (enableLocalization) {
            List<IdName> idNames = udpAclService.getDeptInfoByIds(userCache.getOrgId(), new ArrayList<>(deptIds));
            if (CollectionUtils.isNotEmpty(idNames)) {
                idNameMap = StreamUtil.list2map(idNames, IdName::getId);
            }
        }


        // 查询人员的盘点结果
        for (PrjUserVO dataData : data.getDatas()) {
            dealUserDim(dataData, idNameMap, gridLevelMap, dimUserMap, dimNameMap, xpdDimList);
        }


        return data;
    }

    private void dealUserDim(PrjUserVO prjUser, Map<String, IdName> idNameMap,
        Map<String, String> gridLevelMap,
        Map<String, List<XpdResultUserDimPO>> dimUserMap,
        Map<String, String> dimNameMap, List<XpdDimPO> xpdDimList) {
        String userId = prjUser.getUserId();
        String deptId = prjUser.getDeptId();
        IdName idName = idNameMap.get(deptId);
        if (idName != null) {
            prjUser.setDeptName(idName.getName());
        }

        List<PrjDimScoreDTO> dimensions = new ArrayList<>();
        List<XpdResultUserDimPO> resultUserDimList = dimUserMap.get(userId);
        Map<String, String> dimResultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resultUserDimList)) {
            dimResultMap =
                StreamUtil.list2map(resultUserDimList, XpdResultUserDimPO::getSdDimId, XpdResultUserDimPO::getGridLevelId);
        }

        for (XpdDimPO xpdDim : xpdDimList) {
            PrjDimScoreDTO prjDimScore = new PrjDimScoreDTO();
            String sdDimId = xpdDim.getSdDimId();

            prjDimScore.setDimensionId(sdDimId);
            prjDimScore.setDimensionName(dimNameMap.get(sdDimId));
            String gridLevelId = dimResultMap.get(sdDimId);
            if (gridLevelId != null) {
                prjDimScore.setGridLevelName(gridLevelMap.get(gridLevelId));
            }

            dimensions.add(prjDimScore);
        }
        prjUser.setDimensions(dimensions);

    }

}
